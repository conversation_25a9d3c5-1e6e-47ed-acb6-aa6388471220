import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ChevronRight, Check } from 'lucide-react';

const CTASection = () => {
  const benefits = [
    "Free detailed quotes with no obligations",
    "Experienced, professional painters and craftsmen",
    "Quality materials and expert workmanship",
    "Clean and tidy work areas during and after service",
    "Fully insured and satisfaction guaranteed"
  ];

  return (
    <section className="py-16 bg-rgb-dark relative overflow-hidden">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-rgb-dark/90"></div>
        <img 
          src="https://images.pexels.com/photos/6585764/pexels-photo-6585764.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" 
          alt="Interior painting" 
          className="h-full w-full object-cover"
        />
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Space?
            </h2>
            <p className="text-gray-300 text-lg mb-8">
              Whether you need interior painting, flooring installation, or other home improvement services, our team of professionals is ready to help bring your vision to life.
            </p>
            
            <ul className="space-y-3 mb-8">
              {benefits.map((benefit, index) => (
                <li key={index} className="flex items-start text-gray-200">
                  <Check className="w-5 h-5 text-rgb-green mr-2 mt-1 flex-shrink-0" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
            
            <Link 
              to="/contact" 
              className="bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-3 px-6 rounded-md transition-colors flex items-center justify-center sm:justify-start gap-2"
            >
              Book Now <ChevronRight size={20} />
            </Link>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-white p-8 rounded-xl shadow-2xl"
          >
            <h3 className="text-2xl font-bold text-rgb-dark mb-2">Special Offer</h3>
            <p className="text-gray-500 mb-6">Limited time promotion for new customers</p>
            
            <div className="bg-rgb-green/10 border-l-4 border-rgb-green p-4 mb-6 rounded-r-md">
              <h4 className="font-semibold text-rgb-green text-lg mb-1">10% Off Your First Service</h4>
              <p className="text-gray-600">Book any service and receive 10% off your first project with RGB Color Painting.</p>
            </div>
            
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:justify-between pb-4 border-b border-gray-200">
                <div>
                  <h5 className="font-semibold">Interior Painting</h5>
                  <p className="text-gray-500 text-sm">Per room (walls only)</p>
                </div>
                <div className="font-semibold text-rgb-dark">Starting at $299</div>
              </div>
              
              <div className="flex flex-col sm:flex-row sm:justify-between pb-4 border-b border-gray-200">
                <div>
                  <h5 className="font-semibold">Hardwood Flooring</h5>
                  <p className="text-gray-500 text-sm">Per sq. ft. (installation)</p>
                </div>
                <div className="font-semibold text-rgb-dark">Starting at $6.99</div>
              </div>
              
              <div className="flex flex-col sm:flex-row sm:justify-between">
                <div>
                  <h5 className="font-semibold">Crown Molding</h5>
                  <p className="text-gray-500 text-sm">Per linear foot</p>
                </div>
                <div className="font-semibold text-rgb-dark">Starting at $4.50</div>
              </div>
            </div>
            
            <Link 
              to="/contact" 
              className="mt-8 block w-full bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-3 px-6 rounded-md text-center transition-colors"
            >
              Claim Offer Now
            </Link>
            <p className="text-xs text-gray-500 mt-2 text-center">*Offer valid for new customers only. Cannot be combined with other promotions.</p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;

