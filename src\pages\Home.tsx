import { useEffect, useState } from 'react';
import { X, ChevronDown, ChevronUp } from 'lucide-react';
import { collection, query, where, getDocs, orderBy, doc, updateDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import Hero from '../components/Hero';
import ServicesList from '../components/ServicesList';
import TestimonialSection from '../components/TestimonialSection';
import CTASection from '../components/CTASection';
import PromotionalBanner from '../components/PromotionalBanner';

interface Promotion {
  id: string;
  title: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
  expiry: string;
  expiryDate?: string;
  active: boolean;
}

const Home = () => {
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [loading, setLoading] = useState(true);
  const [bannerPromotion, setBannerPromotion] = useState<Promotion | null>(null);

  useEffect(() => {
    document.title = 'RGB Color Painting | Professional Painting Services';
    
    // Fetch promotions from Firestore
    const fetchPromotions = async () => {
      try {
        setLoading(true);
        console.log("Fetching promotions for Home page...");
        
        // Get active promotions - temporarily remove the active filter for debugging
        const promotionsQuery = query(
          collection(db, "promotions"), 
          orderBy("timestamp", "desc")
        );
        
        const promotionsSnapshot = await getDocs(promotionsQuery);
        console.log(`Found ${promotionsSnapshot.size} promotions in total`);
        
        const promotionsData: Promotion[] = [];
        promotionsSnapshot.forEach((doc) => {
          const data = doc.data();
          console.log("Promotion data:", { id: doc.id, ...data });
          promotionsData.push({
            id: doc.id,
            ...data,
          } as Promotion);
        });
        
        // Log all promotions and then filter active ones
        console.log("All promotions:", promotionsData);
        const activePromotions = promotionsData.filter(promo => promo.active);
        console.log("Active promotions:", activePromotions);
        
        // Set the first active promotion as the banner (if available)
        if (activePromotions.length > 0) {
          setBannerPromotion(activePromotions[0]);
          console.log("Banner promotion set:", activePromotions[0]);
        }
        
        setPromotions(activePromotions); // Only show active promotions
        setLoading(false);
      } catch (error) {
        console.error("Error fetching promotions:", error);
        setLoading(false);
      }
    };
    
    fetchPromotions();
  }, []);

  return (
    <div className="relative">
      {/* Background image for the entire page */}
      <div className="fixed inset-0 z-[-1]">
        <div className="absolute inset-0 bg-black/80"></div>
        <img 
          src="https://images.pexels.com/photos/6585764/pexels-photo-6585764.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" 
          alt="Background" 
          className="h-full w-full object-cover"
        />
      </div>
      
      <Hero />
      
      {/* Featured Promotions Section */}
      {!loading && promotions.length > 0 && (
        <section className="py-16 bg-gray-50/90">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-rgb-dark mb-4">Special Offers</h2>
              <p className="text-gray-600 text-lg">
                Take advantage of our limited-time promotions and save on your next project.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {promotions.map((promo) => (
                <div key={promo.id} className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow flex flex-col h-full">
                  <div className="relative">
                    <img 
                      src={promo.image} 
                      alt={promo.title} 
                      className="w-full h-auto"
                    />
                    {promo.expiry && (
                      <div className="absolute top-4 right-4 bg-rgb-green text-white text-sm font-semibold py-1 px-3 rounded-full">
                        {promo.expiry}
                      </div>
                    )}
                  </div>
                  
                  <div className="p-6 flex-grow flex flex-col">
                    <h3 className="text-xl font-bold text-rgb-dark mb-2">{promo.title}</h3>
                    <p className="text-gray-600 mb-4 flex-grow">{promo.description}</p>
                    
                    <a 
                      href={promo.buttonLink} 
                      className="inline-block bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-2 px-4 rounded-md transition-colors mt-auto"
                    >
                      {promo.buttonText}
                    </a>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <a 
                href="/promotions" 
                className="inline-block bg-white border border-rgb-dark text-rgb-dark font-semibold py-2 px-6 rounded-md transition-colors hover:bg-gray-50"
              >
                View All Promotions
              </a>
            </div>
          </div>
        </section>
      )}
      
      <ServicesList />
      <TestimonialSection />
      <CTASection />
      
      {bannerPromotion && (
        <PromotionalBanner 
          image={bannerPromotion.image}
          title={bannerPromotion.title}
          description={bannerPromotion.description}
          buttonText={bannerPromotion.buttonText}
          buttonLink={bannerPromotion.buttonLink}
          position="bottom"
          dismissible={true}
          delay={3000}
        />
      )}
    </div>
  );
};

export default Home;
