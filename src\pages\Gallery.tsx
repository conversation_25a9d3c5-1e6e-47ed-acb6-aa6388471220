import { useEffect } from 'react';
import GalleryGrid from '../components/GalleryGrid';

const Gallery = () => {
  useEffect(() => {
    document.title = 'Our Work | RGB Color Painting';
  }, []);

  return (
    <>
      <section className="pt-28 pb-16 bg-gradient-to-r from-rgb-dark to-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">Our Work Gallery</h1>
            <p className="text-gray-300 text-lg">
              Browse through our project gallery to see examples of our quality craftsmanship and attention to detail.
            </p>
          </div>
        </div>
      </section>

      <GalleryGrid />
    </>
  );
};

export default Gallery;