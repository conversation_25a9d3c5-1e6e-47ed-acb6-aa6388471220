import { useEffect, useState } from 'react';
import { Palette, Home, Grid, Construction, Lightbulb, DoorOpen } from 'lucide-react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import ServiceCard from './ServiceCard';

interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  image: string;
  icon: string;
  slug: string;
}

const ServicesList = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  // Map icon names to components
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'Palette':
        return <Palette className="w-6 h-6 text-white" />;
      case 'Home':
        return <Home className="w-6 h-6 text-white" />;
      case 'Grid':
        return <Grid className="w-6 h-6 text-white" />;
      case 'Lightbulb':
        return <Lightbulb className="w-6 h-6 text-white" />;
      case 'Construction':
        return <Construction className="w-6 h-6 text-white" />;
      case 'DoorOpen':
        return <DoorOpen className="w-6 h-6 text-white" />;
      default:
        return <Palette className="w-6 h-6 text-white" />;
    }
  };

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const servicesQuery = query(collection(db, "services"), orderBy("timestamp", "desc"));
        const snapshot = await getDocs(servicesQuery);
        
        const servicesData: Service[] = [];
        snapshot.forEach((doc) => {
          servicesData.push({
            id: doc.id,
            ...doc.data()
          } as Service);
        });
        
        setServices(servicesData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching services:", error);
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-rgb-green border-t-transparent"></div>
          <p className="mt-2 text-gray-600">Loading services...</p>
        </div>
      </section>
    );
  }

  if (services.length === 0) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-600">No services available at the moment.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-rgb-dark mb-4">Our Professional Services</h2>
          <p className="text-gray-600 text-lg">
            We offer a comprehensive range of home improvement services to transform your space.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard
              key={service.id}
              title={service.title}
              description={service.description}
              icon={getIconComponent(service.icon)}
              imageUrl={service.image}
              delay={index * 0.1}
              slug={service.slug}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesList;
