import { useEffect, useState } from 'react';
import { Facebook, Instagram, Phone, Mail } from 'lucide-react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import Logo from './Logo';
import { Link } from 'react-router-dom';

interface Service {
  id: string;
  title: string;
  slug: string;
}

const Footer = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        const servicesQuery = query(collection(db, "services"), orderBy("timestamp", "desc"));
        const snapshot = await getDocs(servicesQuery);
        
        const servicesData: Service[] = [];
        snapshot.forEach((doc) => {
          servicesData.push({
            id: doc.id,
            title: doc.data().title,
            slug: doc.data().slug
          });
        });
        
        setServices(servicesData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching services for footer:", error);
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  return (
    <footer className="bg-rgb-dark text-white pt-12 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          <div>
            <div className="inline-block p-2 rounded-md mb-4">
              <Logo />
            </div>
            <p className="text-gray-300 mb-4">
              Professional painting and home improvement services in the GTA. Quality work, trusted service.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://www.facebook.com/share/166Ysx9yJG/?mibextid=wwXIfr"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-blue-600 p-2 rounded-full hover:bg-blue-700 transition-colors"
                aria-label="Facebook"
              >
                <Facebook size={20} />
              </a>
              <a
                href="https://www.instagram.com/rgb_color_painting_?igsh=ZTEydm45MjIzeWtt&utm_source=qr"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-pink-600 p-2 rounded-full hover:bg-pink-700 transition-colors"
                aria-label="Instagram"
              >
                <Instagram size={20} />
              </a>
              <a
                href="https://www.tiktok.com/@rgbcolorpainting?_t=ZM-8vlvGiq2TI6&_r=1"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-black p-2 rounded-full hover:bg-gray-800 transition-colors"
                aria-label="TikTok"
              >
                {/* <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"/>
                  <path d="M15 8a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
                  <path d="M15 8v4a4 4 0 0 1-4 4H9"/>
                  <line x1="15" y1="4" x2="15" y2="8"/>
                </svg> */}
                <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 256 256"
    fill="currentColor"
  >
    <path d="M204.8 81.9a75.36 75.36 0 01-43.5-13.8v71.5a75.34 75.34 0 01-75.3 75.3A75.44 75.44 0 0110.7 139.7a75.43 75.43 0 0161.3-74V113a29.37 29.37 0 0028.3 29.4 29.26 29.26 0 0030.2-29.4V0h44.2a31 31 0 0030.1 31v50.9z"/>
  </svg>
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold mb-4">Our Services</h3>
            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-700 rounded w-3/4 mb-3"></div>
                <div className="h-4 bg-gray-700 rounded w-2/3 mb-3"></div>
                <div className="h-4 bg-gray-700 rounded w-4/5 mb-3"></div>
                <div className="h-4 bg-gray-700 rounded w-3/5 mb-3"></div>
              </div>
            ) : services.length > 0 ? (
              <ul className="space-y-2">
                {services.map(service => (
                  <li key={service.id} className="hover:text-rgb-green transition-colors">
                    <Link to={`/services#${service.slug}`}>{service.title}</Link>
                  </li>
                ))}
              </ul>
            ) : (
              <ul className="space-y-2">
                <li className="hover:text-rgb-green transition-colors">
                  <Link to="/services#painting">Painting</Link>
                </li>
                <li className="hover:text-rgb-green transition-colors">
                  <Link to="/services#flooring">Hardwood & Laminate Flooring</Link>
                </li>
                <li className="hover:text-rgb-green transition-colors">
                  <Link to="/services#backsplash">Tile Backsplashes</Link>
                </li>
                <li className="hover:text-rgb-green transition-colors">
                  <Link to="/services#ceiling">Smooth Ceiling</Link>
                </li>
                <li className="hover:text-rgb-green transition-colors">
                  <Link to="/services#molding">Crown Molding & Baseboards</Link>
                </li>
              </ul>
            )}
          </div>
          
          <div>
            <h3 className="text-xl font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li className="hover:text-rgb-green transition-colors">
                <Link to="/">Home</Link>
              </li>
              <li className="hover:text-rgb-green transition-colors">
                <Link to="/about">About Us</Link>
              </li>
              <li className="hover:text-rgb-green transition-colors">
                <Link to="/services">Services</Link>
              </li>
              <li className="hover:text-rgb-green transition-colors">
                <Link to="/gallery">Gallery</Link>
              </li>
              <li className="hover:text-rgb-green transition-colors">
                <Link to="/contact">Contact</Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-center">
                <Phone size={20} className="mr-3 text-rgb-green" />
                <a href="tel:6475319216" className="hover:text-rgb-green transition-colors">
                  (*************
                </a>
              </li>
              <li className="flex items-center">
                <Mail size={20} className="mr-3 text-rgb-green" />
                <a href="mailto:<EMAIL>" className="hover:text-rgb-green transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="pt-4">
                <Link 
                  to="/contact" 
                  className="bg-rgb-green hover:bg-rgb-green/90 transition-colors text-white py-2 px-4 rounded-md font-medium"
                >
                  Get a Free Quote
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 pt-6 text-center text-gray-400">
          <p>© {new Date().getFullYear()} RGB Color Painting. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
