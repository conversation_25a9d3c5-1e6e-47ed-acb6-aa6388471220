import { useState, useEffect } from 'react';
import { collection, getDocs, query, orderBy, doc, updateDoc, deleteDoc, addDoc, serverTimestamp, where } from 'firebase/firestore';
import { db } from '../firebase/config';
import { 
  Plus, 
  X, 
  ChevronUp, 
  ChevronDown, 
  Palette, 
  Home, 
  Grid, 
  Lightbulb, 
  Construction, 
  DoorOpen 
} from 'lucide-react';

interface Request {
  id: string;
  name: string;
  email: string;
  phone: string;
  service: string;
  message?: string;
  timestamp: any;
  status: string;
  source?: string;
}

interface Promotion {
  id: string;
  title: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
  expiry: string;
  expiryDate?: string; // Add new field for the actual date
  active: boolean;
  timestamp: any;
}

interface Slide {
  id: string;
  title: string;
  description: string;
  image: string;
  order: number;
  active: boolean;
  timestamp: any;
}

interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  image: string;
  icon: string;
  slug: string;
  timestamp: any;
}

const Admin = () => {
  const [requests, setRequests] = useState<Request[]>([]);
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [password, setPassword] = useState('');
  const [authenticated, setAuthenticated] = useState(false);
  const [activeTab, setActiveTab] = useState<'requests' | 'promotions' | 'slides' | 'services'>('requests');
  const [newPromotion, setNewPromotion] = useState<Omit<Promotion, 'id' | 'timestamp'>>({
    title: '',
    description: '',
    image: '',
    buttonText: 'Learn More',
    buttonLink: '/contact',
    expiry: '',
    expiryDate: '', // Initialize the new date field
    active: true
  });
  const [isAddingPromotion, setIsAddingPromotion] = useState(false);
  const [isEditingPromotion, setIsEditingPromotion] = useState(false);
  const [editingPromotion, setEditingPromotion] = useState<Promotion | null>(null);
  const [newSlide, setNewSlide] = useState<Omit<Slide, 'id' | 'timestamp'>>({
    title: '',
    description: '',
    image: '',
    order: 0,
    active: true
  });
  const [isAddingSlide, setIsAddingSlide] = useState(false);
  const [isEditingSlide, setIsEditingSlide] = useState(false);
  const [editingSlide, setEditingSlide] = useState<Slide | null>(null);
  const [newService, setNewService] = useState<Omit<Service, 'id' | 'timestamp'>>({
    title: '',
    description: '',
    features: [''],
    image: '',
    icon: 'Palette',
    slug: ''
  });
  const [isAddingService, setIsAddingService] = useState(false);
  const [isEditingService, setIsEditingService] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // Clear any previous error message when attempting to login
    setError('');
    
    // Simple password protection (replace with proper authentication in production)
    if (password === 'admin123') {
      setAuthenticated(true);
      localStorage.setItem('adminAuthenticated', 'true');
    } else {
      setError('Invalid password');
    }
  };

  // Handle password input change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    // Clear error message when user starts typing again
    if (error) {
      setError('');
    }
  };

  useEffect(() => {
    // Check if already authenticated
    if (localStorage.getItem('adminAuthenticated') === 'true') {
      setAuthenticated(true);
    }
  }, []);

  useEffect(() => {
    if (!authenticated) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch requests
        const requestsQuery = query(collection(db, "requests"), orderBy("timestamp", "desc"));
        const requestsSnapshot = await getDocs(requestsQuery);
        
        const requestsData: Request[] = [];
        requestsSnapshot.forEach((doc) => {
          requestsData.push({
            id: doc.id,
            ...doc.data(),
          } as Request);
        });
        
        setRequests(requestsData);
        
        // Fetch promotions
        const promotionsQuery = query(collection(db, "promotions"), orderBy("timestamp", "desc"));
        const promotionsSnapshot = await getDocs(promotionsQuery);
        
        const promotionsData: Promotion[] = [];
        promotionsSnapshot.forEach((doc) => {
          promotionsData.push({
            id: doc.id,
            ...doc.data(),
          } as Promotion);
        });
        
        setPromotions(promotionsData);

        // Fetch slides
        const slidesQuery = query(collection(db, "slides"), orderBy("order", "asc"));
        const slidesSnapshot = await getDocs(slidesQuery);
        
        const slidesData: Slide[] = [];
        slidesSnapshot.forEach((doc) => {
          slidesData.push({
            id: doc.id,
            ...doc.data(),
          } as Slide);
        });
        
        setSlides(slidesData);

        // Fetch services
        const servicesQuery = query(collection(db, "services"), orderBy("timestamp", "desc"));
        const servicesSnapshot = await getDocs(servicesQuery);
        
        const servicesData: Service[] = [];
        servicesSnapshot.forEach((doc) => {
          servicesData.push({
            id: doc.id,
            ...doc.data(),
          } as Service);
        });
        
        setServices(servicesData);
        
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError('Failed to load data');
        setLoading(false);
      }
    };

    fetchData();
  }, [authenticated]);

  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      await updateDoc(doc(db, "requests", id), {
        status: newStatus
      });
      
      // Update local state
      setRequests(requests.map(request => 
        request.id === id ? { ...request, status: newStatus } : request
      ));
    } catch (error) {
      console.error("Error updating status:", error);
      setError('Failed to update status');
    }
  };

  const handleDeleteRequest = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this request?')) return;
    
    try {
      await deleteDoc(doc(db, "requests", id));
      
      // Update local state
      setRequests(requests.filter(request => request.id !== id));
    } catch (error) {
      console.error("Error deleting request:", error);
      setError('Failed to delete request');
    }
  };

  const handlePromotionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (name === 'expiryDate') {
      // When the date changes, update both the date field and format a display string
      const date = new Date(value);
      const formattedDate = date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
      
      setNewPromotion(prev => ({
        ...prev,
        expiryDate: value,
        expiry: value ? `Until ${formattedDate}` : ''
      }));
    } else {
      setNewPromotion(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
      }));
    }
  };

  const handleSavePromotion = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate form data
      if (!newPromotion.title || !newPromotion.description || !newPromotion.image) {
        throw new Error('Please fill out all required fields');
      }
      
      if (isEditingPromotion && editingPromotion) {
        // Update existing promotion
        console.log("Updating promotion:", editingPromotion.id);
        
        await updateDoc(doc(db, "promotions", editingPromotion.id), {
          ...newPromotion,
          // Don't update the timestamp when editing
        });
        
        // Update local state
        setPromotions(promotions.map(promo => 
          promo.id === editingPromotion.id ? { 
            ...promo, 
            ...newPromotion 
          } : promo
        ));
        
        console.log("Promotion updated successfully");
      } else {
        // Add a new promotion
        console.log("Adding new promotion");
        
        // Add a new document to the "promotions" collection
        const promotionData = {
          ...newPromotion,
          timestamp: serverTimestamp()
        };
        
        const docRef = await addDoc(collection(db, "promotions"), promotionData);
        console.log("Promotion added with ID:", docRef.id);
        
        // Add to local state with the new ID
        const newPromo = {
          ...newPromotion,
          id: docRef.id,
          timestamp: new Date()
        };
        
        setPromotions([newPromo, ...promotions]);
      }
      
      // Reset form and state
      setNewPromotion({
        title: '',
        description: '',
        image: '',
        buttonText: 'Learn More',
        buttonLink: '/contact',
        expiry: '',
        expiryDate: '',
        active: true
      });
      
      setIsAddingPromotion(false);
      setIsEditingPromotion(false);
      setEditingPromotion(null);
    } catch (error) {
      console.error("Error saving promotion:", error);
      setError('Failed to save promotion');
    }
  };

  const handleTogglePromotionStatus = async (id: string, currentStatus: boolean) => {
    try {
      await updateDoc(doc(db, "promotions", id), {
        active: !currentStatus
      });
      
      // Update local state
      setPromotions(promotions.map(promo => 
        promo.id === id ? { ...promo, active: !currentStatus } : promo
      ));
    } catch (error) {
      console.error("Error updating promotion status:", error);
      setError('Failed to update promotion status');
    }
  };

  const handleDeletePromotion = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this promotion?')) return;
    
    try {
      await deleteDoc(doc(db, "promotions", id));
      
      // Update local state
      setPromotions(promotions.filter(promo => promo.id !== id));
    } catch (error) {
      console.error("Error deleting promotion:", error);
      setError('Failed to delete promotion');
    }
  };

  const handleEditPromotion = (promotion: Promotion) => {
    setEditingPromotion(promotion);
    setNewPromotion({
      title: promotion.title,
      description: promotion.description,
      image: promotion.image,
      buttonText: promotion.buttonText,
      buttonLink: promotion.buttonLink,
      expiry: promotion.expiry,
      expiryDate: promotion.expiryDate || '', // Use the stored date or empty string
      active: promotion.active
    });
    setIsEditingPromotion(true);
    setIsAddingPromotion(false);
  };

  const handleSlideChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setNewSlide(prev => ({
      ...prev,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : name === 'order' 
          ? parseInt(value) 
          : value
    }));
  };

  const handleSaveSlide = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate form data
      if (!newSlide.title || !newSlide.description || !newSlide.image) {
        throw new Error('Please fill out all required fields');
      }
      
      if (isEditingSlide && editingSlide) {
        // Update existing slide
        console.log("Updating slide:", editingSlide.id);
        
        await updateDoc(doc(db, "slides", editingSlide.id), {
          ...newSlide,
          // Don't update the timestamp when editing
        });
        
        // Update local state
        setSlides(slides.map(slide => 
          slide.id === editingSlide.id ? { 
            ...slide, 
            ...newSlide 
          } : slide
        ));
        
        console.log("Slide updated successfully");
      } else {
        // Add a new slide
        console.log("Adding new slide");
        
        // Add a new document to the "slides" collection
        const slideData = {
          ...newSlide,
          timestamp: serverTimestamp()
        };
        
        const docRef = await addDoc(collection(db, "slides"), slideData);
        console.log("Slide added with ID:", docRef.id);
        
        // Add to local state with the new ID
        const newSlideWithId = {
          ...newSlide,
          id: docRef.id,
          timestamp: new Date()
        };
        
        setSlides([...slides, newSlideWithId].sort((a, b) => a.order - b.order));
      }
      
      // Reset form and state
      setNewSlide({
        title: '',
        description: '',
        image: '',
        order: slides.length,
        active: true
      });
      
      setIsAddingSlide(false);
      setIsEditingSlide(false);
      setEditingSlide(null);
    } catch (error) {
      console.error("Error saving slide:", error);
      setError('Failed to save slide');
    }
  };

  const handleToggleSlideStatus = async (id: string, currentStatus: boolean) => {
    try {
      await updateDoc(doc(db, "slides", id), {
        active: !currentStatus
      });
      
      // Update local state
      setSlides(slides.map(slide => 
        slide.id === id ? { ...slide, active: !currentStatus } : slide
      ));
    } catch (error) {
      console.error("Error updating slide status:", error);
      setError('Failed to update slide status');
    }
  };

  const handleDeleteSlide = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this slide?')) return;
    
    try {
      await deleteDoc(doc(db, "slides", id));
      
      // Update local state
      setSlides(slides.filter(slide => slide.id !== id));
    } catch (error) {
      console.error("Error deleting slide:", error);
      setError('Failed to delete slide');
    }
  };

  const handleEditSlide = (slide: Slide) => {
    setEditingSlide(slide);
    setNewSlide({
      title: slide.title,
      description: slide.description,
      image: slide.image,
      order: slide.order,
      active: slide.active
    });
    setIsEditingSlide(true);
    setIsAddingSlide(false);
  };

  const handleMoveSlide = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = slides.findIndex(slide => slide.id === id);
    if (
      (direction === 'up' && currentIndex === 0) || 
      (direction === 'down' && currentIndex === slides.length - 1)
    ) {
      return; // Can't move further in this direction
    }
    
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const targetSlide = slides[targetIndex];
    const currentSlide = slides[currentIndex];
    
    try {
      // Update orders in Firestore
      await updateDoc(doc(db, "slides", currentSlide.id), {
        order: targetSlide.order
      });
      
      await updateDoc(doc(db, "slides", targetSlide.id), {
        order: currentSlide.order
      });
      
      // Update local state
      const updatedSlides = [...slides];
      updatedSlides[currentIndex] = { ...currentSlide, order: targetSlide.order };
      updatedSlides[targetIndex] = { ...targetSlide, order: currentSlide.order };
      
      // Sort by order
      setSlides(updatedSlides.sort((a, b) => a.order - b.order));
    } catch (error) {
      console.error("Error reordering slides:", error);
      setError('Failed to reorder slides');
    }
  };

  const handleServiceChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'slug' && !isEditingService) {
      // Auto-generate slug from title if not editing
      setNewService(prev => ({
        ...prev,
        [name]: value.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
      }));
    } else {
      setNewService(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleFeatureChange = (index: number, value: string) => {
    const updatedFeatures = [...newService.features];
    updatedFeatures[index] = value;
    
    setNewService(prev => ({
      ...prev,
      features: updatedFeatures
    }));
  };

  const handleAddFeature = () => {
    setNewService(prev => ({
      ...prev,
      features: [...prev.features, '']
    }));
  };

  const handleRemoveFeature = (index: number) => {
    if (newService.features.length <= 1) return;
    
    const updatedFeatures = [...newService.features];
    updatedFeatures.splice(index, 1);
    
    setNewService(prev => ({
      ...prev,
      features: updatedFeatures
    }));
  };

  const handleSaveService = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate form data
      if (!newService.title || !newService.description || !newService.image || !newService.slug) {
        throw new Error('Please fill out all required fields');
      }
      
      // Filter out empty features
      const filteredFeatures = newService.features.filter(feature => feature.trim() !== '');
      if (filteredFeatures.length === 0) {
        throw new Error('Please add at least one feature');
      }
      
      const serviceData = {
        ...newService,
        features: filteredFeatures
      };
      
      if (isEditingService && editingService) {
        // Update existing service
        console.log("Updating service:", editingService.id);
        
        await updateDoc(doc(db, "services", editingService.id), serviceData);
        
        // Update local state
        setServices(services.map(service => 
          service.id === editingService.id ? { 
            ...service, 
            ...serviceData 
          } : service
        ));
        
        console.log("Service updated successfully");
      } else {
        // Check if slug already exists
        const slugQuery = query(collection(db, "services"), where("slug", "==", serviceData.slug));
        const slugSnapshot = await getDocs(slugQuery);
        
        if (!slugSnapshot.empty) {
          throw new Error('A service with this slug already exists. Please use a different slug.');
        }
        
        // Add a new service
        console.log("Adding new service");
        
        const newServiceData = {
          ...serviceData,
          timestamp: serverTimestamp()
        };
        
        const docRef = await addDoc(collection(db, "services"), newServiceData);
        console.log("Service added with ID:", docRef.id);
        
        // Add to local state with the new ID
        const newServiceWithId = {
          ...serviceData,
          id: docRef.id,
          timestamp: new Date()
        };
        
        setServices([newServiceWithId, ...services]);
      }
      
      // Reset form and state
      setNewService({
        title: '',
        description: '',
        features: [''],
        image: '',
        icon: 'Palette',
        slug: ''
      });
      
      setIsAddingService(false);
      setIsEditingService(false);
      setEditingService(null);
    } catch (error) {
      console.error("Error saving service:", error);
      setError(error instanceof Error ? error.message : 'Failed to save service');
    }
  };

  const handleDeleteService = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this service? This will affect all pages that reference this service.')) return;
    
    try {
      await deleteDoc(doc(db, "services", id));
      
      // Update local state
      setServices(services.filter(service => service.id !== id));
    } catch (error) {
      console.error("Error deleting service:", error);
      setError('Failed to delete service');
    }
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
    setNewService({
      title: service.title,
      description: service.description,
      features: [...service.features],
      image: service.image,
      icon: service.icon,
      slug: service.slug
    });
    setIsEditingService(true);
    setIsAddingService(false);
  };

  const getIconComponent = (iconName: string) => {
    console.log("Icon name:", iconName); // Add this for debugging
    
    switch (iconName) {
      case 'Palette':
        return <Palette className="w-6 h-6 text-white" />;
      case 'Home':
        return <Home className="w-6 h-6 text-white" />;
      case 'Grid':
        return <Grid className="w-6 h-6 text-white" />;
      case 'Lightbulb':
        return <Lightbulb className="w-6 h-6 text-white" />;
      case 'Construction':
        return <Construction className="w-6 h-6 text-white" />;
      case 'DoorOpen':
        return <DoorOpen className="w-6 h-6 text-white" />;
      default:
        console.log("Using default icon for:", iconName);
        return <Palette className="w-6 h-6 text-white" />;
    }
  };
  // Available icons for services
  const availableIcons = [
    { name: 'Palette', component: <Palette className="w-6 h-6" /> },
    { name: 'Home', component: <Home className="w-6 h-6" /> },
    { name: 'Grid', component: <Grid className="w-6 h-6" /> },
    { name: 'Lightbulb', component: <Lightbulb className="w-6 h-6" /> },
    { name: 'Construction', component: <Construction className="w-6 h-6" /> },
    { name: 'DoorOpen', component: <DoorOpen className="w-6 h-6" /> },
  ];

  if (!authenticated) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold text-center mb-6">Admin Login</h2>
          
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          
          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label htmlFor="password" className="block text-gray-700 mb-2">Password</label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={handlePasswordChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full bg-rgb-green text-white py-2 px-4 rounded-md hover:bg-rgb-green/90 transition-colors"
            >
              Login
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <button
            onClick={() => {
              localStorage.removeItem('adminAuthenticated');
              setAuthenticated(false);
            }}
            className="bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
          >
            Logout
          </button>
        </div>
        
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 mb-6 overflow-x-auto">
          <button
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              activeTab === 'requests' 
                ? 'text-rgb-green border-b-2 border-rgb-green' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('requests')}
          >
            Service Requests
          </button>
          <button
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              activeTab === 'services' 
                ? 'text-rgb-green border-b-2 border-rgb-green' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('services')}
          >
            Services
          </button>
          <button
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              activeTab === 'promotions' 
                ? 'text-rgb-green border-b-2 border-rgb-green' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('promotions')}
          >
            Promotions
          </button>
          <button
            className={`py-2 px-4 font-medium whitespace-nowrap ${
              activeTab === 'slides' 
                ? 'text-rgb-green border-b-2 border-rgb-green' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('slides')}
          >
            Hero Slides
          </button>
        </div>
        
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-rgb-green border-t-transparent"></div>
            <p className="mt-2 text-gray-600">Loading data...</p>
          </div>
        ) : activeTab === 'requests' ? (
          // Requests Tab Content
          requests.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-600">No requests found.</p>
            </div>
          ) : (
            <div className="bg-white shadow-md rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {requests.map((request) => (
                      <tr key={request.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{request.name}</div>
                          <div className="text-sm text-gray-500">{request.source || 'Contact Form'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{request.email}</div>
                          <div className="text-sm text-gray-500">{request.phone}</div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">{request.service}</div>
                          {request.message && (
                            <div className="text-sm text-gray-500 max-w-xs truncate">{request.message}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {request.timestamp?.toDate().toLocaleString() || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <select
                            value={request.status}
                            onChange={(e) => handleStatusChange(request.id, e.target.value)}
                            className={`text-sm rounded-full px-3 py-1 font-medium ${
                              request.status === 'new' ? 'bg-blue-100 text-blue-800' :
                              request.status === 'contacted' ? 'bg-yellow-100 text-yellow-800' :
                              request.status === 'completed' ? 'bg-green-100 text-green-800' :
                              'bg-gray-100 text-gray-800'
                            }`}
                          >
                            <option value="new">New</option>
                            <option value="contacted">Contacted</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                          </select>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleDeleteRequest(request.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )
        ) : activeTab === 'promotions' ? (
          // Promotions Tab Content
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">Manage Promotions</h2>
              <button
                onClick={() => setIsAddingPromotion(true)}
                className="bg-rgb-green text-white py-2 px-4 rounded-md hover:bg-rgb-green/90 transition-colors flex items-center gap-2"
              >
                <Plus size={18} /> Add Promotion
              </button>
            </div>
            
            {(isAddingPromotion || isEditingPromotion) && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">
                    {isEditingPromotion ? 'Edit Promotion' : 'New Promotion'}
                  </h3>
                  <button
                    onClick={() => {
                      setIsAddingPromotion(false);
                      setIsEditingPromotion(false);
                      setEditingPromotion(null);
                      setNewPromotion({
                        title: '',
                        description: '',
                        image: '',
                        buttonText: 'Learn More',
                        buttonLink: '/contact',
                        expiry: '',
                        expiryDate: '',
                        active: true
                      });
                    }}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X size={20} />
                  </button>
                </div>
                
                <form onSubmit={handleSavePromotion}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label htmlFor="title" className="block text-gray-700 mb-1">Title*</label>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={newPromotion.title}
                        onChange={handlePromotionChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="expiryDate" className="block text-gray-700 mb-1">
                        <div className="flex items-center">
                          <Calendar size={16} className="mr-1" />
                          <span>Expiry Date</span>
                        </div>
                      </label>
                      <input
                        type="date"
                        id="expiryDate"
                        name="expiryDate"
                        value={newPromotion.expiryDate}
                        onChange={handlePromotionChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        {newPromotion.expiry ? `Will display as: ${newPromotion.expiry}` : 'Select a date to set expiry'}
                      </p>
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="description" className="block text-gray-700 mb-1">Description*</label>
                      <textarea
                        id="description"
                        name="description"
                        value={newPromotion.description}
                        onChange={handlePromotionChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="image" className="block text-gray-700 mb-1">Image URL*</label>
                      <input
                        type="url"
                        id="image"
                        name="image"
                        value={newPromotion.image}
                        onChange={handlePromotionChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="buttonText" className="block text-gray-700 mb-1">Button Text</label>
                      <input
                        type="text"
                        id="buttonText"
                        name="buttonText"
                        value={newPromotion.buttonText}
                        onChange={handlePromotionChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="buttonLink" className="block text-gray-700 mb-1">Button Link</label>
                      <input
                        type="text"
                        id="buttonLink"
                        name="buttonLink"
                        value={newPromotion.buttonLink}
                        onChange={handlePromotionChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="active" className="block text-gray-700 mb-1">Active</label>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="active"
                          name="active"
                          checked={newPromotion.active}
                          onChange={handlePromotionChange}
                          className="mr-2"
                        />
                        <span className="text-gray-700">Enable this promotion</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-4">
                    <button
                      type="submit"
                      className="bg-rgb-green text-white py-2 px-4 rounded-md hover:bg-rgb-green/90 transition-colors"
                    >
                      {isEditingPromotion ? 'Update Promotion' : 'Add Promotion'}
                    </button>
                    
                    {isEditingPromotion && (
                      <button
                        type="button"
                        onClick={() => {
                          setIsEditingPromotion(false);
                          setEditingPromotion(null);
                          setNewPromotion({
                            title: '',
                            description: '',
                            image: '',
                            buttonText: 'Learn More',
                            buttonLink: '/contact',
                            expiry: '',
                            expiryDate: '',
                            active: true
                          });
                        }}
                        className="bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </form>
              </div>
            )}
            
            <div className="bg-white shadow-md rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {promotions.map((promotion) => (
                      <tr key={promotion.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{promotion.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{promotion.expiry}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm rounded-full px-3 py-1 font-medium ${promotion.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {promotion.active ? 'Active' : 'Inactive'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center space-x-4">
                            <button
                              onClick={() => handleEditPromotion(promotion)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleTogglePromotionStatus(promotion.id, promotion.active)}
                              className={`text-${promotion.active ? 'gray' : 'green'}-600 hover:text-${promotion.active ? 'gray' : 'green'}-900`}
                            >
                              {promotion.active ? 'Deactivate' : 'Activate'}
                            </button>
                            <button
                              onClick={() => handleDeletePromotion(promotion.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : activeTab === 'slides' ? (
          // Slides Tab Content
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">Manage Hero Slides</h2>
              <button
                onClick={() => {
                  setIsAddingSlide(true);
                  setIsEditingSlide(false);
                  setEditingSlide(null);
                  setNewSlide({
                    title: '',
                    description: '',
                    image: '',
                    order: slides.length,
                    active: true
                  });
                }}
                className="bg-rgb-green text-white py-2 px-4 rounded-md hover:bg-rgb-green/90 transition-colors flex items-center gap-2"
              >
                <Plus size={18} /> Add Slide
              </button>
            </div>
            
            {(isAddingSlide || isEditingSlide) && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">
                    {isEditingSlide ? 'Edit Slide' : 'New Slide'}
                  </h3>
                  <button
                    onClick={() => {
                      setIsAddingSlide(false);
                      setIsEditingSlide(false);
                      setEditingSlide(null);
                      setNewSlide({
                        title: '',
                        description: '',
                        image: '',
                        order: slides.length,
                        active: true
                      });
                    }}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X size={20} />
                  </button>
                </div>
                
                <form onSubmit={handleSaveSlide}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label htmlFor="title" className="block text-gray-700 mb-1">Title*</label>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={newSlide.title}
                        onChange={handleSlideChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="order" className="block text-gray-700 mb-1">Display Order</label>
                      <input
                        type="number"
                        id="order"
                        name="order"
                        value={newSlide.order}
                        onChange={handleSlideChange}
                        min="0"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="description" className="block text-gray-700 mb-1">Description*</label>
                      <textarea
                        id="description"
                        name="description"
                        value={newSlide.description}
                        onChange={handleSlideChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="image" className="block text-gray-700 mb-1">Image URL*</label>
                      <input
                        type="url"
                        id="image"
                        name="image"
                        value={newSlide.image}
                        onChange={handleSlideChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="active" className="block text-gray-700 mb-1">Active</label>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="active"
                          name="active"
                          checked={newSlide.active}
                          onChange={handleSlideChange}
                          className="mr-2"
                        />
                        <span className="text-gray-700">Enable this slide</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-4">
                    <button
                      type="submit"
                      className="bg-rgb-green text-white py-2 px-4 rounded-md hover:bg-rgb-green/90 transition-colors"
                    >
                      {isEditingSlide ? 'Update Slide' : 'Add Slide'}
                    </button>
                    
                    {isEditingSlide && (
                      <button
                        type="button"
                        onClick={() => {
                          setIsEditingSlide(false);
                          setEditingSlide(null);
                          setNewSlide({
                            title: '',
                            description: '',
                            image: '',
                            order: slides.length,
                            active: true
                          });
                        }}
                        className="bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </form>
              </div>
            )}
            
            <div className="bg-white shadow-md rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {slides.map((slide, index) => (
                      <tr key={slide.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900">{slide.order}</span>
                            <div className="flex flex-col">
                              <button
                                onClick={() => handleMoveSlide(slide.id, 'up')}
                                disabled={index === 0}
                                className={`text-gray-500 ${index === 0 ? 'opacity-30 cursor-not-allowed' : 'hover:text-gray-700'}`}
                              >
                                <ChevronUp size={16} />
                              </button>
                              <button
                                onClick={() => handleMoveSlide(slide.id, 'down')}
                                disabled={index === slides.length - 1}
                                className={`text-gray-500 ${index === slides.length - 1 ? 'opacity-30 cursor-not-allowed' : 'hover:text-gray-700'}`}
                              >
                                <ChevronDown size={16} />
                              </button>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <img 
                            src={slide.image} 
                            alt={slide.title} 
                            className="h-16 w-24 object-cover rounded"
                          />
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-900">{slide.title}</div>
                          <div className="text-sm text-gray-500 max-w-xs truncate">{slide.description}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm rounded-full px-3 py-1 font-medium ${slide.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                            {slide.active ? 'Active' : 'Inactive'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center space-x-4">
                            <button
                              onClick={() => handleEditSlide(slide)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleToggleSlideStatus(slide.id, slide.active)}
                              className={`text-${slide.active ? 'gray' : 'green'}-600 hover:text-${slide.active ? 'gray' : 'green'}-900`}
                            >{slide.active ? 'Deactivate' : 'Activate'}
                            </button>
                            <button
                              onClick={() => handleDeleteSlide(slide.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : (
          // Services Tab Content
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">Manage Services</h2>
              <button
                onClick={() => {
                  setIsAddingService(true);
                  setIsEditingService(false);
                  setEditingService(null);
                  setNewService({
                    title: '',
                    description: '',
                    features: [''],
                    image: '',
                    icon: 'Palette',
                    slug: ''
                  });
                }}
                className="bg-rgb-green text-white py-2 px-4 rounded-md hover:bg-rgb-green/90 transition-colors flex items-center gap-2"
              >
                <Plus size={18} /> Add Service
              </button>
            </div>
            
            {(isAddingService || isEditingService) && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">
                    {isEditingService ? 'Edit Service' : 'New Service'}
                  </h3>
                  <button
                    onClick={() => {
                      setIsAddingService(false);
                      setIsEditingService(false);
                      setEditingService(null);
                      setNewService({
                        title: '',
                        description: '',
                        features: [''],
                        image: '',
                        icon: 'Palette',
                        slug: ''
                      });
                    }}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <X size={20} />
                  </button>
                </div>
                
                <form onSubmit={handleSaveService}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label htmlFor="title" className="block text-gray-700 mb-1">Title*</label>
                      <input
                        type="text"
                        id="title"
                        name="title"
                        value={newService.title}
                        onChange={handleServiceChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="slug" className="block text-gray-700 mb-1">Slug* (URL-friendly name)</label>
                      <input
                        type="text"
                        id="slug"
                        name="slug"
                        value={newService.slug}
                        onChange={handleServiceChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                        pattern="[a-z0-9-]+"
                        title="Lowercase letters, numbers, and hyphens only"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Used in URLs. Only lowercase letters, numbers, and hyphens.
                      </p>
                    </div>
                    
                    {/* <div>
                      <label htmlFor="icon" className="block text-gray-700 mb-1">Icon*</label>
                      <select
                        id="icon"
                        name="icon"
                        value={newService.icon}
                        onChange={handleServiceChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      >
                        <option value="Palette">Palette</option>
                        <option value="Home">Home</option>
                        <option value="Grid">Grid</option>
                        <option value="Lightbulb">Lightbulb</option>
                        <option value="Construction">Construction</option>
                        <option value="DoorOpen">Door Open</option>
                      </select>
                    </div> */}

                    <div>
                      <label htmlFor="icon" className="block text-gray-700 mb-1">Icon*</label>
                      <select
                        id="icon"
                        name="icon"
                        value={newService.icon}
                        onChange={handleServiceChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      >
                        {availableIcons.map((icon) => (
                          <option key={icon.name} value={icon.name}>
                            {icon.name}
                          </option>
                        ))}
                      </select>
                      <div className="mt-2 flex space-x-2">
                        {availableIcons.map((icon) => (
                          <div 
                            key={icon.name}
                            className={`p-2 rounded-md cursor-pointer ${
                              newService.icon === icon.name ? 'bg-rgb-green text-white' : 'bg-gray-100'
                            }`}
                            onClick={() => setNewService(prev => ({ ...prev, icon: icon.name }))}
                          >
                            {icon.component}
                          </div>
                        ))}
                      </div>
                    </div>
              
                    
                    <div>
                      <label htmlFor="image" className="block text-gray-700 mb-1">Image URL*</label>
                      <input
                        type="url"
                        id="image"
                        name="image"
                        value={newService.image}
                        onChange={handleServiceChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="description" className="block text-gray-700 mb-1">Description*</label>
                      <textarea
                        id="description"
                        name="description"
                        value={newService.description}
                        onChange={handleServiceChange}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                        required
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-gray-700 mb-1">Features*</label>
                      {newService.features.map((feature, index) => (
                        <div key={index} className="flex items-center mb-2">
                          <input
                            type="text"
                            value={feature}
                            onChange={(e) => handleFeatureChange(index, e.target.value)}
                            className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                            placeholder={`Feature ${index + 1}`}
                          />
                          <button
                            type="button"
                            onClick={() => handleRemoveFeature(index)}
                            className="ml-2 text-red-500 hover:text-red-700"
                            disabled={newService.features.length <= 1}
                          >
                            <X size={20} />
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={handleAddFeature}
                        className="text-rgb-green hover:text-rgb-green/80 font-medium"
                      >
                        + Add Feature
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex space-x-4">
                    <button
                      type="submit"
                      className="bg-rgb-green text-white py-2 px-4 rounded-md hover:bg-rgb-green/90 transition-colors"
                    >
                      {isEditingService ? 'Update Service' : 'Add Service'}
                    </button>
                    
                    {isEditingService && (
                      <button
                        type="button"
                        onClick={() => {
                          setIsEditingService(false);
                          setEditingService(null);
                          setNewService({
                            title: '',
                            description: '',
                            features: [''],
                            image: '',
                            icon: 'Palette',
                            slug: ''
                          });
                        }}
                        className="bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
                      >
                        Cancel
                      </button>
                    )}
                  </div>
                </form>
              </div>
            )}
            
            <div className="bg-white shadow-md rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Icon</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Features</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {services.map((service) => (
                      <tr key={service.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{service.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">
                            <span className="inline-block w-8 h-8 bg-rgb-green rounded-full flex items-center justify-center">
                              {getIconComponent(service.icon)}
                            </span>
                            <span className="ml-2">{service.icon}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{service.slug}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {service.features.join(', ')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center space-x-4">
                            <button
                              onClick={() => handleEditService(service)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteService(service.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Admin;
