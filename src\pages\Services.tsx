import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Palette, Home, Grid, Lightbulb, Construction, DoorOpen, Check } from 'lucide-react';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';

interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  image: string;
  icon: string;
  slug: string;
}

const Services = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    document.title = 'Our Services | RGB Color Painting';
    
    const fetchServices = async () => {
      try {
        setLoading(true);
        const servicesQuery = query(collection(db, "services"), orderBy("timestamp", "desc"));
        const snapshot = await getDocs(servicesQuery);
        
        const servicesData: Service[] = [];
        snapshot.forEach((doc) => {
          servicesData.push({
            id: doc.id,
            ...doc.data()
          } as Service);
        });
        
        setServices(servicesData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching services:", error);
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Map icon names to components
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'Palette':
        return <Palette className="w-8 h-8 text-rgb-green" />;
      case 'Home':
        return <Home className="w-8 h-8 text-rgb-green" />;
      case 'Grid':
        return <Grid className="w-8 h-8 text-rgb-green" />;
      case 'Lightbulb':
        return <Lightbulb className="w-8 h-8 text-rgb-green" />;
      case 'Construction':
        return <Construction className="w-8 h-8 text-rgb-green" />;
      case 'DoorOpen':
        return <DoorOpen className="w-8 h-8 text-rgb-green" />;
      default:
        return <Palette className="w-8 h-8 text-rgb-green" />;
    }
  };

  if (loading) {
    return (
      <section className="pt-28 pb-16">
        <div className="container mx-auto px-4 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-rgb-green border-t-transparent"></div>
          <p className="mt-2 text-gray-600">Loading services...</p>
        </div>
      </section>
    );
  }

  return (
    <>
      <section className="pt-28 pb-16 bg-gradient-to-r from-rgb-dark to-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">Our Professional Services</h1>
            <p className="text-gray-300 text-lg">
              We offer a comprehensive range of home improvement services to transform your space with quality craftsmanship and attention to detail.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          {services.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600 text-lg">No services available at the moment.</p>
            </div>
          ) : (
            <div className="space-y-24">
              {services.map((service, index) => (
                <div 
                  key={service.id} 
                  id={service.slug}
                  className="scroll-mt-24"
                >
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true }}
                    className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                      index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                    }`}
                  >
                    <div className={index % 2 === 1 ? 'lg:order-2' : ''}>
                      <div className="flex items-center mb-4">
                        {getIconComponent(service.icon)}
                        <h2 className="text-3xl font-bold text-rgb-dark ml-3">{service.title}</h2>
                      </div>
                      
                      <p className="text-gray-600 text-lg mb-6">
                        {service.description}
                      </p>
                      
                      <ul className="space-y-3 mb-6">
                        {service.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <Check className="w-5 h-5 text-rgb-green mr-2 mt-1 flex-shrink-0" />
                            <span className="text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                      
                      <div className="mt-6">
                        <a 
                          href="/contact" 
                          className="inline-block bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-3 px-6 rounded-md transition-colors"
                        >
                          Get a Quote
                        </a>
                      </div>
                    </div>
                    
                    <div className={index % 2 === 1 ? 'lg:order-1' : ''}>
                      <div className="rounded-lg overflow-hidden shadow-xl">
                        <img 
                          src={service.image} 
                          alt={service.title} 
                          className="w-full h-auto object-cover"
                        />
                      </div>
                    </div>
                  </motion.div>
                  
                  {index < services.length - 1 && (
                    <div className="border-b border-gray-200 w-full my-12"></div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </>
  );
};

export default Services;
