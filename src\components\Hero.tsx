import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { collection, getDocs, query, orderBy } from "firebase/firestore";
import { db } from "../firebase/config";

interface Service {
  id: string;
  title: string;
  slug: string;
}

interface Slide {
  id: string;
  title: string;
  description: string;
  image: string;
  order: number;
  active: boolean;
  timestamp: any;
}

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [formData, setFormData] = useState({
    service: "",
    name: "",
    phone: "",
    email: "",
    message: "",
  });
  const [services, setServices] = useState<Service[]>([]);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [loading, setLoading] = useState(true);
  const [slidesLoading, setSlidesLoading] = useState(true);

  // Fetch services from Firestore
  useEffect(() => {
    const fetchServices = async () => {
      try {
        const servicesQuery = query(
          collection(db, "services"),
          orderBy("timestamp", "desc")
        );
        const snapshot = await getDocs(servicesQuery);

        const servicesData: Service[] = [];
        snapshot.forEach((doc) => {
          servicesData.push({
            id: doc.id,
            title: doc.data().title,
            slug: doc.data().slug,
          });
        });

        setServices(servicesData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching services for hero form:", error);
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  // Fetch slides from Firestore
  useEffect(() => {
    const fetchSlides = async () => {
      try {
        setSlidesLoading(true);
        console.log("Fetching slides for hero slider...");

        // First try to get all slides, then filter and sort in JavaScript
        console.log("Fetching slides from Firestore...");
        const slidesQuery = query(
          collection(db, "slides"),
          orderBy("order", "asc")
        );
        const snapshot = await getDocs(slidesQuery);
        console.log(`Total slides in database: ${snapshot.size}`);

        const slidesData: Slide[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          // Only include active slides
          if (data.active === true) {
            slidesData.push({
              id: doc.id,
              title: data.title,
              description: data.description,
              image: data.image,
              order: data.order,
              active: data.active,
              timestamp: data.timestamp,
            });
          }
        });

        console.log(`Found ${slidesData.length} active slides`);

        if (slidesData.length === 0) {
          console.log(
            "No active slides found in database. Creating default slides for demonstration."
          );
          // Create some default slides to show the slider functionality
          const defaultSlides: Slide[] = [
            {
              id: "demo-1",
              title: "Transform Your Space with Professional Painting",
              description:
                "High-quality interior and exterior painting services for residential and commercial properties in the Greater Toronto Area.",
              image:
                "https://images.pexels.com/photos/6585764/pexels-photo-6585764.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
              order: 0,
              active: true,
              timestamp: new Date(),
            },
            {
              id: "demo-2",
              title: "Expert Flooring Installation",
              description:
                "Premium hardwood, laminate, and tile flooring installation by experienced professionals.",
              image:
                "https://images.pexels.com/photos/4846097/pexels-photo-4846097.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
              order: 1,
              active: true,
              timestamp: new Date(),
            },
            {
              id: "demo-3",
              title: "Complete Home Renovation Services",
              description:
                "From minor updates to major renovations, we handle all aspects of your home improvement project.",
              image:
                "https://images.pexels.com/photos/4792480/pexels-photo-4792480.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
              order: 2,
              active: true,
              timestamp: new Date(),
            },
            {
              id: "demo-4",
              title: "Crown Molding & Trim Installation",
              description:
                "Add elegance and value to your home with our precision crown molding, baseboards, and custom trim work.",
              image:
                "https://images.pexels.com/photos/6474471/pexels-photo-6474471.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
              order: 3,
              active: true,
              timestamp: new Date(),
            },
          ];
          setSlides(defaultSlides);
        } else {
          setSlides(slidesData);
        }
        setSlidesLoading(false);
      } catch (error) {
        console.error("Error fetching slides for hero slider:", error);
        setSlidesLoading(false);
        // Set default slides if there's an error fetching from database
        console.log("Using default slides due to fetch error");
        const defaultSlides: Slide[] = [
          {
            id: "error-demo-1",
            title: "Transform Your Space with Professional Painting",
            description:
              "High-quality interior and exterior painting services for residential and commercial properties in the Greater Toronto Area.",
            image:
              "https://images.pexels.com/photos/6585764/pexels-photo-6585764.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
            order: 0,
            active: true,
            timestamp: new Date(),
          },
          {
            id: "error-demo-2",
            title: "Expert Flooring Installation",
            description:
              "Premium hardwood, laminate, and tile flooring installation by experienced professionals.",
            image:
              "https://images.pexels.com/photos/4846097/pexels-photo-4846097.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
            order: 1,
            active: true,
            timestamp: new Date(),
          },
          {
            id: "error-demo-3",
            title: "Complete Home Renovation Services",
            description:
              "From minor updates to major renovations, we handle all aspects of your home improvement project.",
            image:
              "https://images.pexels.com/photos/4792480/pexels-photo-4792480.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
            order: 2,
            active: true,
            timestamp: new Date(),
          },
          {
            id: "error-demo-4",
            title: "Crown Molding & Trim Installation",
            description:
              "Add elegance and value to your home with our precision crown molding, baseboards, and custom trim work.",
            image:
              "https://images.pexels.com/photos/6474471/pexels-photo-6474471.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
            order: 3,
            active: true,
            timestamp: new Date(),
          },
        ];
        setSlides(defaultSlides);
      }
    };

    fetchSlides();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Reset form
    setFormData({
      service: "",
      name: "",
      phone: "",
      email: "",
      message: "",
    });
  };

  // Auto-rotate slides
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  // Handle manual navigation
  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const goToPrevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  const goToNextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  // Show loading state if slides are still being fetched
  if (slidesLoading) {
    return (
      <section className="relative h-screen">
        <div className="absolute inset-0 bg-gradient-to-r from-rgb-dark to-gray-900"></div>
        <div className="relative z-20 h-full flex items-center justify-center">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rgb-green mx-auto mb-4"></div>
            <p className="text-lg">Loading slides...</p>
          </div>
        </div>
      </section>
    );
  }

  // Show message if no slides are available
  if (slides.length === 0) {
    return (
      <section className="relative h-screen">
        <div className="absolute inset-0 bg-gradient-to-r from-rgb-dark to-gray-900"></div>
        <div className="relative z-20 h-full flex items-center justify-center">
          <div className="text-white text-center max-w-2xl mx-auto px-4">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Welcome to RGB Color Painting
            </h1>
            <p className="text-xl mb-8 text-gray-200">
              Professional painting and home improvement services in the Greater
              Toronto Area.
            </p>
            <div className="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-4 mb-8">
              <p className="text-yellow-200">
                <strong>Admin Notice:</strong> No slides configured. Please add
                slides in the admin panel to display the image slider.
              </p>
            </div>
            <div className="flex flex-wrap gap-4 justify-center">
              <Link
                to="/contact"
                className="bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-3 px-6 rounded-md transition-colors inline-flex items-center"
              >
                Get a Free Quote <ChevronRight className="ml-2 w-5 h-5" />
              </Link>
              <Link
                to="/services"
                className="bg-white hover:bg-gray-100 text-rgb-dark font-semibold py-3 px-6 rounded-md transition-colors"
              >
                Our Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative h-screen">
      {/* Slider */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-black/60 z-10"></div>

        <div
          className="flex transition-transform duration-700 ease-in-out h-full"
          style={{
            width: `${slides.length * 100}%`,
            transform: `translateX(-${currentSlide * (100 / slides.length)}%)`,
          }}
        >
          {slides.map((slide) => (
            <div
              key={slide.id}
              className="relative"
              style={{ width: `${100 / slides.length}%` }}
            >
              <img
                src={slide.image}
                alt={slide.title}
                className="h-full w-full object-cover"
              />
            </div>
          ))}
        </div>

        {/* Slider controls */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full ${
                currentSlide === index ? "bg-rgb-green" : "bg-white/50"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        <button
          onClick={goToPrevSlide}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full"
          aria-label="Previous slide"
        >
          <ChevronLeft size={24} />
        </button>

        <button
          onClick={goToNextSlide}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full"
          aria-label="Next slide"
        >
          <ChevronRight size={24} />
        </button>
      </div>

      {/* Content */}
      <div className="relative z-20 h-full flex items-center">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-white text-center lg:text-left lg:max-w-2xl"
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                {slides[currentSlide]?.title || "Welcome to RGB Color Painting"}
              </h1>

              <p className="text-xl md:text-2xl mb-8 text-gray-200">
                {slides[currentSlide]?.description ||
                  "Professional painting and home improvement services"}
              </p>

              <div className="flex flex-wrap gap-4">
                <Link
                  to="/contact"
                  className="bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-3 px-6 rounded-md transition-colors inline-flex items-center"
                >
                  Get a Free Quote <ChevronRight className="ml-2 w-5 h-5" />
                </Link>

                <Link
                  to="/services"
                  className="bg-white hover:bg-gray-100 text-rgb-dark font-semibold py-3 px-6 rounded-md transition-colors"
                >
                  Our Services
                </Link>
              </div>
            </motion.div>

            <div className="hidden lg:block">
              <motion.div
                initial={{ opacity: 10, y: 10 }}
                animate={{ opacity: 10, y: 40 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-lg shadow-xl p-4"
              >
                <h2 className="text-2xl font-bold text-rgb-dark mb-6">
                  Request a Free Quote
                </h2>
                <form className="space-y-3" onSubmit={handleSubmit}>
                  <div>
                    <label
                      htmlFor="service"
                      className="block text-gray-700 mb-1"
                    >
                      Service
                    </label>
                    <select
                      id="service"
                      value={formData.service}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                    >
                      <option value="">Select a service</option>
                      {loading ? (
                        <option value="" disabled>
                          Loading services...
                        </option>
                      ) : services.length > 0 ? (
                        services.map((service) => (
                          <option key={service.id} value={service.slug}>
                            {service.title}
                          </option>
                        ))
                      ) : (
                        <>
                          <option value="interior-painting">
                            Interior Painting
                          </option>
                          <option value="exterior-painting">
                            Exterior Painting
                          </option>
                          <option value="commercial-painting">
                            Commercial Painting
                          </option>
                          <option value="hardwood-flooring">
                            Hardwood Flooring
                          </option>
                          <option value="laminate-flooring">
                            Laminate Flooring
                          </option>
                          <option value="home-renovation">
                            Home Renovation
                          </option>
                        </>
                      )}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="name" className="block text-gray-700 mb-1">
                      Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                      placeholder="Your name"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-gray-700 mb-1">
                      Phone
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                      placeholder="Your phone number"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-gray-700 mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                      placeholder="Your email"
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-2 px-6 rounded-md transition-colors"
                  >
                    Submit Request
                  </button>
                </form>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
