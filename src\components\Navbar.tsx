import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { Menu, X, Phone } from 'lucide-react';
import Logo from './Logo';

interface NavbarProps {
  scrolled: boolean;
}

const Navbar = ({ scrolled }: NavbarProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header
      className={`fixed w-full z-30 transition-all duration-300 ${
        scrolled ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <NavLink to="/" className="flex items-center z-10" onClick={closeMenu}>
          <Logo scrolled={scrolled} />
        </NavLink>

        <div className="hidden md:flex items-center space-x-8">
          <nav className="flex items-center space-x-6">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `text-lg font-semibold transition-colors ${
                  scrolled 
                    ? (isActive ? 'text-rgb-green' : 'text-rgb-dark hover:text-rgb-green') 
                    : (isActive ? 'text-rgb-green' : 'text-white hover:text-rgb-green')
                }`
              }
            >
              Home
            </NavLink>
            <NavLink
              to="/services"
              className={({ isActive }) =>
                `text-lg font-semibold transition-colors ${
                  scrolled 
                    ? (isActive ? 'text-rgb-green' : 'text-rgb-dark hover:text-rgb-green') 
                    : (isActive ? 'text-rgb-green' : 'text-white hover:text-rgb-green')
                }`
              }
            >
              Services
            </NavLink>
            <NavLink
              to="/gallery"
              className={({ isActive }) =>
                `text-lg font-semibold transition-colors ${
                  scrolled 
                    ? (isActive ? 'text-rgb-green' : 'text-rgb-dark hover:text-rgb-green') 
                    : (isActive ? 'text-rgb-green' : 'text-white hover:text-rgb-green')
                }`
              }
            >
              Gallery
            </NavLink>
            <NavLink
              to="/about"
              className={({ isActive }) =>
                `text-lg font-semibold transition-colors ${
                  scrolled 
                    ? (isActive ? 'text-rgb-green' : 'text-rgb-dark hover:text-rgb-green') 
                    : (isActive ? 'text-rgb-green' : 'text-white hover:text-rgb-green')
                }`
              }
            >
              About
            </NavLink>
            <NavLink
              to="/contact"
              className={({ isActive }) =>
                `text-lg font-semibold transition-colors ${
                  scrolled 
                    ? (isActive ? 'text-rgb-green' : 'text-rgb-dark hover:text-rgb-green') 
                    : (isActive ? 'text-rgb-green' : 'text-white hover:text-rgb-green')
                }`
              }
            >
              Contact
            </NavLink>
          </nav>
          
          <a
            href="tel:6475319216"
            className="flex items-center gap-2 bg-rgb-green text-white px-4 py-2 rounded-md font-medium hover:bg-rgb-green/90 transition-colors"
          >
            <Phone size={18} />
            <span className="font-semibold">(*************</span>
          </a>
        </div>

        <button
          onClick={toggleMenu}
          className="md:hidden text-rgb-dark z-10"
          aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
        >
          {isMenuOpen ? <X size={28} /> : <Menu size={28} />}
        </button>

        {/* Mobile Menu */}
        <div
          className={`fixed inset-0 bg-white z-0 transform transition-transform duration-300 ease-in-out ${
            isMenuOpen ? 'translate-x-0' : 'translate-x-full'
          } md:hidden flex flex-col justify-center items-center`}
        >
          <nav className="flex flex-col items-center space-y-8 mb-12">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `text-2xl font-medium ${isActive ? 'text-rgb-green' : 'text-rgb-dark'}`
              }
              onClick={closeMenu}
            >
              Home
            </NavLink>
            <NavLink
              to="/services"
              className={({ isActive }) =>
                `text-2xl font-medium ${isActive ? 'text-rgb-green' : 'text-rgb-dark'}`
              }
              onClick={closeMenu}
            >
              Services
            </NavLink>
            <NavLink
              to="/gallery"
              className={({ isActive }) =>
                `text-2xl font-medium ${isActive ? 'text-rgb-green' : 'text-rgb-dark'}`
              }
              onClick={closeMenu}
            >
              Gallery
            </NavLink>
            <NavLink
              to="/about"
              className={({ isActive }) =>
                `text-2xl font-medium ${isActive ? 'text-rgb-green' : 'text-rgb-dark'}`
              }
              onClick={closeMenu}
            >
              About
            </NavLink>
            <NavLink
              to="/contact"
              className={({ isActive }) =>
                `text-2xl font-medium ${isActive ? 'text-rgb-green' : 'text-rgb-dark'}`
              }
              onClick={closeMenu}
            >
              Contact
            </NavLink>
          </nav>
          <a
            href="tel:6475319216"
            className="flex items-center gap-2 bg-rgb-green text-white px-6 py-3 rounded-md text-lg font-medium"
            onClick={closeMenu}
          >
            <Phone size={20} />
            <span>(*************</span>
          </a>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
