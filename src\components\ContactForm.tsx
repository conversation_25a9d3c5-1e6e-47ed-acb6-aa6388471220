import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AtSign, Phone, MessageSquare, Send, Check } from 'lucide-react';
import { collection, addDoc, serverTimestamp, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';

const ContactForm = () => {
  const [formStatus, setFormStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });
  const [errorMessage, setErrorMessage] = useState('');

  // Test Firebase connectivity on component mount
  useEffect(() => {
    const testFirebaseConnection = async () => {
      try {
        console.log("Testing Firebase connection...");
        // Just try to get a reference to the collection
        const querySnapshot = await getDocs(collection(db, "requests"));
        console.log("Firebase connection successful, found", querySnapshot.size, "documents");
      } catch (error) {
        console.error("Firebase connection error:", error);
      }
    };
    
    testFirebaseConnection();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormStatus('submitting');
    
    try {
      // Validate form data
      if (!formData.name || !formData.email || !formData.phone || !formData.service) {
        throw new Error('Please fill out all required fields');
      }
      
      console.log('Submitting form data:', formData);
      
      // Add a new document to the "requests" collection
      const docRef = await addDoc(collection(db, "requests"), {
        ...formData,
        timestamp: serverTimestamp(),
        status: 'new'
      });
      
      console.log('Document written with ID: ', docRef.id);
      setFormStatus('success');
      // Reset form after success
      setTimeout(() => {
        setFormData({
          name: '',
          email: '',
          phone: '',
          service: '',
          message: ''
        });
        setFormStatus('idle');
      }, 5000);
    } catch (error) {
      console.error("Error submitting form:", error);
      setErrorMessage(error instanceof Error ? error.message : 'There was an error submitting your request. Please try again.');
      setFormStatus('error');
      setTimeout(() => {
        setFormStatus('idle');
      }, 5000);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-xl p-6 md:p-8">
      <h3 className="text-2xl font-bold text-rgb-dark mb-6">Book Now</h3>
      
      {formStatus === 'success' ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start"
        >
          <Check className="text-green-500 w-6 h-6 mr-3 mt-0.5" />
          <div>
            <h4 className="font-semibold text-green-800">Message Sent Successfully!</h4>
            <p className="text-green-700">Thank you for reaching out. We'll get back to you shortly to discuss your project.</p>
          </div>
        </motion.div>
      ) : formStatus === 'error' ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start"
        >
          <div className="text-red-500 w-6 h-6 mr-3 mt-0.5">!</div>
          <div>
            <h4 className="font-semibold text-red-800">Error</h4>
            <p className="text-red-700">{errorMessage}</p>
          </div>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-gray-700 mb-1 font-medium">
              Full Name <span className="text-rgb-red">*</span>
            </label>
            <div className="relative">
              <input
                type="text"
                id="name"
                name="name"
                required
                value={formData.name}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                placeholder="John Doe"
              />
              <div className="absolute left-3 top-2.5 text-rgb-gray">
                <AtSign size={18} />
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="email" className="block text-gray-700 mb-1 font-medium">
                Email Address <span className="text-rgb-red">*</span>
              </label>
              <div className="relative">
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                  placeholder="<EMAIL>"
                />
                <div className="absolute left-3 top-2.5 text-rgb-gray">
                  <AtSign size={18} />
                </div>
              </div>
            </div>
            
            <div>
              <label htmlFor="phone" className="block text-gray-700 mb-1 font-medium">
                Phone Number <span className="text-rgb-red">*</span>
              </label>
              <div className="relative">
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  required
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                  placeholder="(*************"
                />
                <div className="absolute left-3 top-2.5 text-rgb-gray">
                  <Phone size={18} />
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <label htmlFor="service" className="block text-gray-700 mb-1 font-medium">
              Service Needed <span className="text-rgb-red">*</span>
            </label>
            <select
              id="service"
              name="service"
              required
              value={formData.service}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
            >
              <option value="">Select a service</option>
              <option value="interior-painting">Interior Painting</option>
              <option value="exterior-painting">Exterior Painting</option>
              <option value="hardwood-flooring">Hardwood Flooring</option>
              <option value="laminate-flooring">Laminate Flooring</option>
              <option value="tile-backsplash">Tile Backsplash</option>
              <option value="smooth-ceiling">Smooth Ceiling</option>
              <option value="crown-molding">Crown Molding</option>
              <option value="baseboard-installation">Baseboard Installation</option>
              <option value="door-installation">Door Installation</option>
              <option value="other">Other Service</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="message" className="block text-gray-700 mb-1 font-medium">
              Project Details
            </label>
            <div className="relative">
              <textarea
                id="message"
                name="message"
                rows={4}
                value={formData.message}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-rgb-green"
                placeholder="Tell us about your project..."
              ></textarea>
              <div className="absolute left-3 top-2.5 text-rgb-gray">
                <MessageSquare size={18} />
              </div>
            </div>
          </div>
          
          <button
            type="submit"
            disabled={formStatus === 'submitting'}
            className={`w-full flex items-center justify-center gap-2 bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-3 px-6 rounded-md transition-colors ${
              formStatus === 'submitting' ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {formStatus === 'submitting' ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </>
            ) : (
              <>
                Book Now <Send size={18} />
              </>
            )}
          </button>
          
          <p className="text-sm text-gray-500 text-center">
            By submitting this form, you agree to be contacted about our services.
          </p>
        </form>
      )}
    </div>
  );
};

export default ContactForm;


