import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';

interface ServiceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  imageUrl: string;
  delay?: number;
  slug: string;
}

const ServiceCard = ({ title, description, icon, imageUrl, delay = 0, slug }: ServiceCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true }}
      className="group bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
    >
      <div className="relative h-48 overflow-hidden">
        <img 
          src={imageUrl} 
          alt={title} 
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
          <div className="p-4 text-white">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-rgb-green rounded-full mb-3">
              {icon}
            </div>
            <h3 className="text-xl font-bold">{title}</h3>
          </div>
        </div>
      </div>
      <div className="p-5">
        <p className="text-gray-600 mb-4">{description}</p>
        <Link 
          to={`/services#${slug}`} 
          className="inline-flex items-center text-rgb-green font-medium hover:text-rgb-green/80 transition-colors"
        >
          Learn More <ChevronRight size={18} className="ml-1" />
        </Link>
      </div>
    </motion.div>
  );
};

export default ServiceCard;