import { useEffect, useState } from 'react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import { motion } from 'framer-motion';

interface Promotion {
  id: string;
  title: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
  expiry: string;
  active: boolean;
}

const Promotions = () => {
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    document.title = 'Special Promotions | RGB Color Painting';
    
    // Fetch promotions from Firestore
    const fetchPromotions = async () => {
      try {
        setLoading(true);
        console.log("Fetching promotions for Promotions page...");
        
        // Get active promotions - temporarily remove the active filter for debugging
        const promotionsQuery = query(
          collection(db, "promotions"), 
          orderBy("timestamp", "desc")
        );
        
        const promotionsSnapshot = await getDocs(promotionsQuery);
        console.log(`Found ${promotionsSnapshot.size} promotions in total`);
        
        const promotionsData: Promotion[] = [];
        promotionsSnapshot.forEach((doc) => {
          const data = doc.data();
          console.log("Promotion data:", { id: doc.id, ...data });
          promotionsData.push({
            id: doc.id,
            ...data,
          } as Promotion);
        });
        
        // Log all promotions and then filter active ones
        console.log("All promotions:", promotionsData);
        const activePromotions = promotionsData.filter(promo => promo.active);
        console.log("Active promotions:", activePromotions);
        
        setPromotions(activePromotions); // Show all for debugging
        setLoading(false);
      } catch (error) {
        console.error("Error fetching promotions:", error);
        setLoading(false);
      }
    };
    
    fetchPromotions();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 pt-24 pb-16">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-rgb-dark mb-4">Special Promotions</h1>
          <p className="text-gray-600 text-lg">
            Take advantage of our limited-time offers and save on your next home improvement project.
          </p>
        </div>
        
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-rgb-green border-t-transparent"></div>
            <p className="mt-2 text-gray-600">Loading promotions...</p>
          </div>
        ) : promotions.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow-md max-w-2xl mx-auto">
            <p className="text-gray-600 text-lg">No active promotions at the moment. Check back soon!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {promotions.map((promo, index) => (
              <motion.div
                key={promo.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow flex flex-col h-full"
              >
                <div className="relative">
                  <img 
                    src={promo.image} 
                    alt={promo.title} 
                    className="w-full h-auto" // Changed from h-64 object-cover to h-auto
                  />
                  {promo.expiry && (
                    <div className="absolute top-4 right-4 bg-rgb-green text-white text-sm font-semibold py-1 px-3 rounded-full">
                      {promo.expiry}
                    </div>
                  )}
                </div>
                
                <div className="p-6 flex-grow flex flex-col">
                  <h2 className="text-2xl font-bold text-rgb-dark mb-3">{promo.title}</h2>
                  <p className="text-gray-600 mb-6 text-lg flex-grow">{promo.description}</p>
                  
                  <a 
                    href={promo.buttonLink} 
                    className="inline-block bg-rgb-green hover:bg-rgb-green/90 text-white font-semibold py-3 px-6 rounded-md transition-colors mt-auto"
                  >
                    {promo.buttonText}
                  </a>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Promotions;
