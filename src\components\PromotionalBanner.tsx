import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import { Link } from 'react-router-dom';

interface PromotionalBannerProps {
  image: string;
  title: string;
  description: string; // Keep this in props for backward compatibility
  buttonText: string;
  buttonLink: string;
  position?: 'top' | 'bottom';
  dismissible?: boolean;
  delay?: number;
}

const PromotionalBanner = ({
  image,
  title,
  description, // Keep this in props but don't use it
  buttonText, // Keep this in props but don't use it
  buttonLink,
  position = 'bottom',
  dismissible = true,
  delay = 2000
}: PromotionalBannerProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);
  
  // Check if this banner was previously dismissed
  useEffect(() => {
    const bannerDismissed = localStorage.getItem(`banner-${title}-dismissed`);
    if (bannerDismissed) {
      setIsDismissed(true);
    } else {
      // Show banner after delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, delay);
      
      return () => clearTimeout(timer);
    }
  }, [title, delay]);
  
  const handleDismiss = () => {
    setIsVisible(false);
    
    // After animation completes, mark as dismissed
    setTimeout(() => {
      setIsDismissed(true);
      localStorage.setItem(`banner-${title}-dismissed`, 'true');
    }, 300);
  };
  
  if (isDismissed) {
    return null;
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 100, x: 100 }}
      animate={{ 
        opacity: isVisible ? 1 : 0, 
        y: isVisible ? 0 : 100,
        x: isVisible ? 0 : 100
      }}
      transition={{ duration: 0.3 }}
      className="fixed bottom-6 right-6 z-50 w-full max-w-xs"
    >
      <div className="bg-white rounded-lg shadow-xl overflow-hidden max-h-[50vh] cursor-pointer">
        <div className="relative h-full">
          {dismissible && (
            <button
              onClick={handleDismiss}
              className="absolute top-2 right-2 bg-gray-800/50 hover:bg-gray-800/70 text-white p-1 rounded-full z-10 transition-colors"
              aria-label="Close promotion"
            >
              <X size={20} />
            </button>
          )}
          
          <Link to={buttonLink} className="block h-full">
            <div className="relative overflow-hidden" style={{ maxHeight: 'calc(50vh)' }}>
              <img 
                src={image} 
                alt={title} 
                className="w-full h-auto object-cover"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                <h3 className="text-lg font-bold text-white">{title}</h3>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default PromotionalBanner;
