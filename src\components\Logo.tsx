interface LogoProps {
  scrolled: boolean;
}

const Logo = ({ scrolled }: LogoProps) => {
  return (
    <div className="flex items-center space-x-3">
      <img 
        src="/images/rgb-logo.png" 
        alt="RGB Color Painting Logo" 
        className="h-20 md:h-20"
      />
      <span
        className={`text-xl md:text-3xl font-bold transition-colors duration-300 ${
          scrolled ? 'text-rgb-dark' : 'text-white'
        }`}
      >
        RGB Color Painting
      </span>
    </div>
  );
};

export default Logo;
