import { motion } from 'framer-motion';
import { Star } from 'lucide-react';

const testimonials = [
  {
    name: "<PERSON>",
    role: "Homeowner",
    content: "RGB Color Painting did an amazing job painting the interior of our home. The team was professional, punctual, and the quality of work exceeded our expectations. Highly recommend!",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Business Owner",
    content: "We hired RGB Color Painting for our office renovation, and we couldn't be happier with the results. The flooring installation was flawless, and they completed the project on time and within budget.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Homeowner",
    content: "The crown molding and baseboard installation transformed our living room. The attention to detail and craftsmanship was outstanding. Will definitely use their services again.",
    rating: 5
  }
];

const TestimonialCard = ({ testimonial, index }: { testimonial: typeof testimonials[0], index: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-white p-6 rounded-xl shadow-md"
    >
      <div className="flex mb-4">
        {[...Array(testimonial.rating)].map((_, i) => (
          <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
        ))}
      </div>
      <p className="text-gray-600 mb-6 italic">"{testimonial.content}"</p>
      <div className="flex items-center">
        <div className="bg-rgb-green/20 rounded-full w-12 h-12 flex items-center justify-center text-rgb-green font-bold">
          {testimonial.name.charAt(0)}
        </div>
        <div className="ml-3">
          <h4 className="font-semibold text-rgb-dark">{testimonial.name}</h4>
          <p className="text-gray-500 text-sm">{testimonial.role}</p>
        </div>
      </div>
    </motion.div>
  );
};

const TestimonialSection = () => {
  return (
    <section className="py-16 bg-gradient-to-br from-rgb-green/5 to-rgb-blue/5">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-rgb-dark mb-4">What Our Clients Say</h2>
          <p className="text-gray-600 text-lg">
            Don't just take our word for it. Read what our satisfied customers have to say about our services.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard key={index} testimonial={testimonial} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;