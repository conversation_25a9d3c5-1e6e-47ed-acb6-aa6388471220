import { useEffect } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Phone, Mail, Clock } from 'lucide-react';
import ContactForm from '../components/ContactForm';

const Contact = () => {
  useEffect(() => {
    document.title = 'Contact Us | RGB Color Painting';
  }, []);

  return (
    <>
      <section className="pt-28 pb-16 bg-gradient-to-r from-rgb-dark to-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">Contact Us</h1>
            <p className="text-gray-300 text-lg">
              Have questions or ready for a quote? We're here to help with your painting and home improvement needs.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-3xl font-bold text-rgb-dark mb-6">Get In Touch</h2>
              <p className="text-gray-600 text-lg mb-8">
                We're ready to help with your painting and home improvement projects. Contact us today for a free quote or to discuss your needs.
              </p>
              
              <div className="space-y-6 mb-8">
                <div className="flex items-start">
                  <div className="bg-rgb-green/10 p-3 rounded-full mr-4">
                    <Phone className="w-6 h-6 text-rgb-green" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-rgb-dark text-lg">Phone</h3>
                    <a href="tel:6475319216" className="text-gray-600 hover:text-rgb-green transition-colors">
                      (*************
                    </a>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-rgb-green/10 p-3 rounded-full mr-4">
                    <Mail className="w-6 h-6 text-rgb-green" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-rgb-dark text-lg">Email</h3>
                    <a href="mailto:<EMAIL>" className="text-gray-600 hover:text-rgb-green transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-rgb-green/10 p-3 rounded-full mr-4">
                    <MapPin className="w-6 h-6 text-rgb-green" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-rgb-dark text-lg">Service Area</h3>
                    <p className="text-gray-600">
                      Greater Toronto Area (GTA)
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-rgb-green/10 p-3 rounded-full mr-4">
                    <Clock className="w-6 h-6 text-rgb-green" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-rgb-dark text-lg">Business Hours</h3>
                    <p className="text-gray-600">
                      Monday - Friday: 8:00 AM - 6:00 PM<br />
                      Saturday: 9:00 AM - 4:00 PM<br />
                      Sunday: Closed
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <a 
                  href="https://www.facebook.com/share/166Ysx9yJG/?mibextid=wwXIfr" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors"
                  aria-label="Facebook"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                  </svg>
                </a>
                <a 
                  href="https://www.instagram.com/rgb_color_painting_?igsh=ZTEydm45MjIzeWtt&utm_source=qr" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="bg-pink-600 text-white p-2 rounded-full hover:bg-pink-700 transition-colors"
                  aria-label="Instagram"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                  </svg>
                </a>
                <a 
                  href="https://www.tiktok.com/@rgbcolorpainting?_t=ZM-8vlvGiq2TI6&_r=1" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="bg-black text-white p-2 rounded-full hover:bg-gray-800 transition-colors"
                  aria-label="TikTok"
                >
                  {/* <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"/>
                    <path d="M15 8a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
                    <path d="M15 8v8a4 4 0 0 1-4 4"/>
                    <line x1="15" y1="2" x2="15" y2="8"/>
                  </svg> */}
                  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 256 256"
    fill="currentColor"
  >
    <path d="M204.8 81.9a75.36 75.36 0 01-43.5-13.8v71.5a75.34 75.34 0 01-75.3 75.3A75.44 75.44 0 0110.7 139.7a75.43 75.43 0 0161.3-74V113a29.37 29.37 0 0028.3 29.4 29.26 29.26 0 0030.2-29.4V0h44.2a31 31 0 0030.1 31v50.9z"/>
  </svg>
                </a>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <ContactForm />
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Contact;